from datetime import date, datetime
import logging
from typing import Sequence

from constants.message import (
    EXTRACTED_DATA_DATE_INTERVAL_MISSING,
    EXTRACTED_DATA_LDMF_MISSING,
    EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING,
    EXTRACTED_DATA_OUTCOMES_MISSING,
    NEED_INFO_INITIAL_PROMPT,
)
from schemas import AggregatedData, ClientNameOption, ConfirmedData, DatePickerOption, LDMFCountryOption, Option


__all__ = ['SystemMessageGenerationService']


logger = logging.getLogger(__name__)


class SystemMessageGenerationService:
    """
    Service for generating system messages based on extracted data.

    This service consolidates the system message generation logic from Azure Durable Functions
    into the main FastAPI application for better maintainability and immediate response generation.
    """

    def format_extracted_data_message(self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData) -> str:
        """
        Format aggregated data into a user-friendly system message content.

        Args:
            aggregated_data: The aggregated data from all sources

        Returns:
            Formatted message content as HTML string
        """
        if aggregated_data.all_fields_none or not aggregated_data.client_name:
            return NEED_INFO_INITIAL_PROMPT
        if not aggregated_data.ldmf_country:
            return EXTRACTED_DATA_LDMF_MISSING
        if not aggregated_data.date_intervals:
            return EXTRACTED_DATA_DATE_INTERVAL_MISSING
        if not aggregated_data.objective_and_scope:
            return EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING
        if not aggregated_data.outcomes:
            return EXTRACTED_DATA_OUTCOMES_MISSING
        return 'All data is complete'  # should not be returned, aggregated_data.is_complete was caught earlier

    def generate_options(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData | None = None
    ) -> Sequence[Option]:
        """
        Generate user selection options based on aggregated data and confirmed data.

        Args:
            aggregated_data: The aggregated data from all sources
            confirmed_data: The confirmed data from previous user selections

        Returns:
            List of option dictionaries for user selection
        """
        options = []
        if aggregated_data.all_fields_none:
            return options

        # Client Names - highest priority (only if not confirmed)
        if (
            aggregated_data.client_name
            and len(aggregated_data.client_name) > 1
            and (not confirmed_data or not confirmed_data.client_name)
        ):
            options.extend([ClientNameOption(client_name=name) for name in aggregated_data.client_name])
            return options

        # Lead Member Firm Countries - second priority (only if not confirmed)
        if (
            aggregated_data.ldmf_country
            and len(aggregated_data.ldmf_country) > 1
            and (not confirmed_data or not confirmed_data.ldmf_country)
        ):
            options.extend([LDMFCountryOption(ldmf_country=country) for country in aggregated_data.ldmf_country])
            return options

        # Engagement Dates - third priority (only if not confirmed or ambiguous)
        if aggregated_data.date_intervals and (not confirmed_data or not confirmed_data.date_intervals):
            for start_date_str, end_date_str in aggregated_data.date_intervals:
                if start_date_str or end_date_str:
                    if self._are_dates_ambiguous(start_date_str, end_date_str):
                        start_date = self._parse_date_string(start_date_str) if start_date_str else None
                        end_date = self._parse_date_string(end_date_str) if end_date_str else None
                        options.append(DatePickerOption(start_date=start_date, end_date=end_date))
                        return options

        return options

    @staticmethod
    def _parse_date_string(date_str: str) -> date | None:
        """
        Parse date string to date object.

        Args:
            date_str: Date string in ISO format (YYYY-MM-DD)

        Returns:
            Date object or None if parsing fails
        """
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError) as e:
            logger.warning('Failed to parse date string "%s": %s', date_str, e)
            return None

    @staticmethod
    def _are_dates_ambiguous(start_date: str | None, end_date: str | None) -> bool:
        """
        Check if dates are ambiguous (day <= 12 could be misinterpreted) or incomplete.

        Args:
            start_date: Start date string in ISO format (YYYY-MM-DD)
            end_date: End date string in ISO format (YYYY-MM-DD)

        Returns:
            True if any date has day <= 12 (ambiguous) or dates are incomplete, False otherwise
        """
        if not start_date or not end_date:
            return True

        processed_start_date = date.fromisoformat(start_date)
        processed_end_date = date.fromisoformat(end_date)

        return processed_start_date.day <= 12 or processed_end_date.day <= 12
