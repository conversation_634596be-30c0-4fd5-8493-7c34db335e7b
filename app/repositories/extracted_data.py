import logging
from uuid import UUI<PERSON>

from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from constants.extracted_data import DataSourceType
from exceptions import EntityNotFoundError
from models.qual_extracted_data import QualExtractedData
from schemas import ExtractedData

from .conversation import ConversationRepository


__all__ = ['ExtractedDataRepository']
logger = logging.getLogger(__name__)


class ExtractedDataRepository:
    """Repository for extracted data-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def create(self, conversation_id: UUID, data_source_type: DataSourceType):
        """
        Create a new extracted data record.

        Args:
            conversation_id: The ID of the conversation
            data_source_type: The type of source data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        extracted_data = QualExtractedData(
            QualConversationId=conversation_internal_id,
            DataSourceType=data_source_type,
        )
        self.db_session.add(extracted_data)
        await self.db_session.flush()

    async def get(self, conversation_id: UUID, data_source_type: DataSourceType) -> ExtractedData | None:
        """
        Get extracted data.

        Args:
            conversation_id: Public conversation ID
            data_source_type: Type of source data to retrieve

        Returns:
            The extracted data if found, None otherwise
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        result = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == data_source_type,
                )
            )
        ).scalar_one_or_none()
        if not result:
            return None

        result.ConversationPublicId = conversation_id
        return ExtractedData.model_validate(result)

    async def update(self, extracted_data: ExtractedData):
        """
        Upsert extracted data of a specified source type for a conversation.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(extracted_data.conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(extracted_data.conversation_id))

        extracted_data_db = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == extracted_data.data_source_type,
                )
            )
        ).scalar_one_or_none()

        if extracted_data_db:
            # Only update fields that have meaningful new values
            new_data = extracted_data.model_dump_for_db(exclude_none=True)
            for key, val in new_data.items():
                if self._should_update_field(key, val, getattr(extracted_data_db, key, None)):
                    setattr(extracted_data_db, key, val)
                    logger.debug(f"Updated {key} to {val}")
        else:
            extracted_data_db = QualExtractedData(
                QualConversationId=conversation_internal_id, **extracted_data.model_dump_for_db()
            )
            self.db_session.add(extracted_data_db)

        await self.db_session.flush()

    def _should_update_field(self, field_name: str, new_value, existing_value) -> bool:
        """
        Determine if a field should be updated based on the new value and existing value.

        Args:
            field_name: Name of the field being updated
            new_value: New value from extraction
            existing_value: Current value in database

        Returns:
            True if field should be updated, False otherwise
        """
        # Always update if there's no existing value
        if existing_value is None:
            return True

        # For list fields (ClientName, LDMFCountry), check if new value is meaningful
        if field_name in ('ClientName', 'LDMFCountry'):
            # Don't update if new value is empty list (serialized as "[]")
            if new_value == '[]':
                return False
            # Update if new value has content and existing is empty
            if existing_value == '[]' and new_value != '[]':
                return True
            # Update if new value has content (let aggregation logic handle merging)
            return new_value != '[]'

        # For string fields, don't update if new value is empty/whitespace
        if isinstance(new_value, str):
            if not new_value or new_value.strip() == '':
                return False

        # For date fields, don't update if new value would overwrite existing date with None
        if field_name in ('StartDate', 'EndDate'):
            if new_value is None and existing_value is not None:
                return False

        # Update in all other cases (new value has meaningful content)
        return True

    async def delete(self, conversation_id: UUID, data_source_type: DataSourceType) -> None:
        """
        Delete extracted data of a specified source type for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data for.
            data_source_type: The type of data source to delete extracted data for.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = delete(QualExtractedData).where(
            QualExtractedData.QualConversationId == conversation_internal_id,
            QualExtractedData.DataSourceType == data_source_type,
        )
        await self.db_session.execute(query)
        await self.db_session.flush()

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data associated with a conversation.

        Args:
            conversation_id: The ID of the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = delete(QualExtractedData).where(QualExtractedData.QualConversationId == conversation_internal_id)
        await self.db_session.execute(query)
        await self.db_session.flush()
