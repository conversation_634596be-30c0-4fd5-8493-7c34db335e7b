{"Intentions": [{"intentionName": "undefined", "description": "A user asks for something that is not related to the project or Qual Generation", "userMessageExamples": ["How can I add a user to my ClickUp account?", "What is the best basketball player in US?", "I really need to update my instagram profile image, can you help?"]}, {"intentionName": "generate_qual", "description": "The user asks to create or generate a qualitative analysis (qual) based on data, project or other information. All data is provided in the prompt.", "userMessageExamples": ["Please generate a qual based on this project", "Can you create a qual for these survey results?"]}, {"intentionName": "extraction", "description": "A user starts to provide information about their project.", "userMessageExamples": ["Client name is Deloitte US", "Our sales increased by 15% in April 2023.", "We're helping Acme Inc. with their digital", "Well the goals were much higher than the reality we've got", "This time we focused on Canadian department, instead of Taiwanese"]}, {"intentionName": "example", "description": "A user asks for an example, a template or a demonstration of something. This can be a direct request to show somthing, or a question about what datapoints to include", "userMessageExamples": ["Show me an example prompt", "Can you give me an example prompt?", "Show me a template for data analysis", "I don't get how should I approach this task. Can you give me an example of these fields filled in with come real project info?"]}, {"intentionName": "dash_discard", "description": "The user wants to reject, ignore or discard certain information on dashboard(selecting dash task) and instead wants to provide data in prompts.", "userMessageExamples": ["Discard this analysis please", "I dont need this section, remove it", "No, I don't want", "No, skip tasks", "No, create new qual"]}, {"intentionName": "uncertainty", "description": "A user expresses uncertainty about what to do next. This can be a request for help, a question about the next steps, or a general inquiry about the process.", "userMessageExamples": ["I'm new here, what should I write?", "Not sure what to do", "How do I create a qual?", "What information do you need?", "What do i do next? Can I use this bot as my personal assistant, for example?", "Are you capable of creating my report?", "Can you even act as a report generator?"]}, {"intentionName": "need_context", "description": "A user wants to work with some data. It can be a request for a description, request to generate Qual or a request to provide any information about the project. As the user did not provide any information about the project, this request will need more context.", "userMessageExamples": ["Write a brief description of my project", "Please generate a qual based on this project", "Could you provide a brief summary of what i've done?", "What client is my report about?"]}, {"intentionName": "user_confirmation", "description": "A user provides active agreement with previous question.", "userMessageExamples": ["Yes", "Yes, this is correct", "Yes, this is the correct information", "Yep", "Correct", "That's right", "Affirmative", "You got it", "Spot on", "Precisely", "Exactly", "Absolutely", "Totally", "That's accurate", "Correct statement", "I agree", "Indeed"]}, {"intentionName": "change_engagement_dates", "description": "A user wants to change engagement dates.", "userMessageExamples": ["I need to change the project start date.", "Can we reschedule the engagement for next month?", "The client wants to push back the deadline to Friday.", "Could you update the end date of this project?", "We need to adjust the timeline for this engagement.", "Is it possible to move the kickoff meeting to a later date?", "I'd like to change the dates for this report.", "The project dates need to be modified.", "Can we extend the engagement period?", "I want to change when this engagement is scheduled to end."]}]}